<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方松院连锁门店数据看板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        /* 标题栏样式 */
        .header {
            height: 80px;
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 40px;
            box-shadow: 0 4px 20px rgba(0, 150, 255, 0.3);
            position: relative;
            z-index: 1000;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(0, 150, 255, 0.1) 50%, transparent 100%);
            animation: headerGlow 3s ease-in-out infinite alternate;
        }

        @keyframes headerGlow {
            0% { opacity: 0.3; }
            100% { opacity: 0.8; }
        }

        .header h1 {
            font-size: 32px;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            position: relative;
            z-index: 1;
        }

        .header-controls {
            display: flex;
            gap: 20px;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .datetime {
            font-size: 18px;
            color: #a0d8ff;
        }

        .control-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .control-group label {
            color: #a0d8ff;
            font-size: 14px;
        }

        .control-group select {
            background: rgba(0, 150, 255, 0.1);
            border: 1px solid #0096ff;
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }

        .control-group select:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
        }

        /* 主要内容区域 */
        .main-content {
            height: calc(100vh - 80px);
            display: grid;
            grid-template-rows: 1fr 300px;
            gap: 20px;
            padding: 20px;
        }

        /* 上半部分：地图和指标 */
        .top-section {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            position: relative;
        }

        /* 地图容器 */
        .map-container {
            position: relative;
            background: rgba(0, 20, 40, 0.8);
            border-radius: 10px;
            border: 1px solid rgba(0, 150, 255, 0.3);
            overflow: hidden;
        }

        #map-chart {
            width: 100%;
            height: 100%;
        }

        /* 指标卡片容器 */
        .metrics-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 20px;
            background: rgba(0, 20, 40, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(0, 150, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .metric-card {
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 100, 200, 0.05) 100%);
            border: 1px solid rgba(0, 150, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 150, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .metric-card:hover::before {
            left: 100%;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 150, 255, 0.3);
        }

        .metric-label {
            font-size: 12px;
            color: #a0d8ff;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .metric-value {
            font-size: 20px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 5px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .metric-change {
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .metric-change.positive {
            color: #00ff88;
        }

        .metric-change.negative {
            color: #ff4757;
        }

        .change-arrow {
            font-size: 10px;
        }

        /* 排行榜容器 */
        .rankings-container {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 300px;
            background: rgba(0, 20, 40, 0.9);
            border-radius: 10px;
            border: 1px solid rgba(0, 150, 255, 0.3);
            padding: 20px;
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .ranking-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid rgba(0, 150, 255, 0.2);
        }

        .ranking-tab {
            flex: 1;
            padding: 8px 4px;
            text-align: center;
            font-size: 12px;
            color: #a0d8ff;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .ranking-tab.active {
            color: #00d4ff;
            border-bottom-color: #00d4ff;
        }

        .ranking-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(0, 150, 255, 0.1);
        }

        .ranking-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
            font-size: 14px;
        }

        .ranking-number.first {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #000;
        }

        .ranking-number.second {
            background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
            color: #000;
        }

        .ranking-number.third {
            background: linear-gradient(135deg, #cd7f32, #daa520);
            color: #fff;
        }

        .ranking-number.other {
            background: rgba(0, 150, 255, 0.2);
            color: #00d4ff;
        }

        .ranking-info {
            flex: 1;
        }

        .ranking-name {
            font-size: 14px;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .ranking-progress {
            width: 100%;
            height: 6px;
            background: rgba(0, 150, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
        }

        .ranking-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #0096ff);
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .ranking-value {
            font-size: 12px;
            color: #a0d8ff;
            margin-top: 4px;
        }

        /* 下半部分：趋势图表 */
        .bottom-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .chart-container {
            background: rgba(0, 20, 40, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(0, 150, 255, 0.3);
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .chart-title {
            font-size: 16px;
            color: #00d4ff;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 500;
        }

        .chart {
            width: 100%;
            height: 220px;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 150, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 150, 255, 0.5);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 150, 255, 0.7);
        }
    </style>
</head>
<body>
    <!-- 标题栏 -->
    <div class="header">
        <h1>方松院连锁门店数据看板</h1>
        <div class="header-controls">
            <div class="datetime" id="datetime"></div>
            <div class="control-group">
                <label>门店:</label>
                <select id="storeSelect">
                    <option value="all">全部门店</option>
                    <option value="store1">北京朝阳店</option>
                    <option value="store2">上海浦东店</option>
                    <option value="store3">广州天河店</option>
                    <option value="store4">深圳南山店</option>
                    <option value="store5">杭州西湖店</option>
                </select>
            </div>
            <div class="control-group">
                <label>时间:</label>
                <select id="timeSelect">
                    <option value="today">今日</option>
                    <option value="yesterday">昨日</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- 上半部分：地图和指标 -->
        <div class="top-section">
            <!-- 地图容器 -->
            <div class="map-container">
                <div id="map-chart"></div>

                <!-- 排行榜容器 -->
                <div class="rankings-container">
                    <div class="ranking-tabs">
                        <div class="ranking-tab active" data-type="revenue">营业额</div>
                        <div class="ranking-tab" data-type="traffic">客流</div>
                        <div class="ranking-tab" data-type="staff">人数</div>
                        <div class="ranking-tab" data-type="commission">提成</div>
                    </div>
                    <div class="ranking-list" id="rankingList">
                        <!-- 排行榜内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 指标卡片容器 -->
            <div class="metrics-container">
                <div class="metric-card">
                    <div class="metric-label">总营业额</div>
                    <div class="metric-value" id="totalRevenue">¥0</div>
                    <div class="metric-change positive" id="revenueChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总现金流</div>
                    <div class="metric-value" id="totalCashFlow">¥0</div>
                    <div class="metric-change positive" id="cashFlowChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总客流</div>
                    <div class="metric-value" id="totalTraffic">0人</div>
                    <div class="metric-change positive" id="trafficChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总耗卡金额</div>
                    <div class="metric-value" id="totalCardConsumption">¥0</div>
                    <div class="metric-change positive" id="cardConsumptionChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总美团金额</div>
                    <div class="metric-value" id="totalMeituan">¥0</div>
                    <div class="metric-change positive" id="meituanChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总抖音金额</div>
                    <div class="metric-value" id="totalDouyin">¥0</div>
                    <div class="metric-change positive" id="douyinChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总线上消费金额</div>
                    <div class="metric-value" id="totalOnline">¥0</div>
                    <div class="metric-change positive" id="onlineChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总线下消费金额</div>
                    <div class="metric-value" id="totalOffline">¥0</div>
                    <div class="metric-change positive" id="offlineChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">总卡金额</div>
                    <div class="metric-value" id="totalCard">¥0</div>
                    <div class="metric-change positive" id="cardChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">好评总量</div>
                    <div class="metric-value" id="totalReviews">0条</div>
                    <div class="metric-change positive" id="reviewsChange">
                        <span class="change-arrow">↗</span>
                        <span>+0%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下半部分：趋势图表 -->
        <div class="bottom-section">
            <div class="chart-container">
                <div class="chart-title">总营业额趋势</div>
                <div class="chart" id="revenueChart"></div>
            </div>
            <div class="chart-container">
                <div class="chart-title">总客流趋势</div>
                <div class="chart" id="trafficChart"></div>
            </div>
            <div class="chart-container">
                <div class="chart-title">总充值金额趋势</div>
                <div class="chart" id="rechargeChart"></div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = {
            stores: {
                all: {
                    metrics: {
                        totalRevenue: { value: 2856789, change: 12.5 },
                        totalCashFlow: { value: 1234567, change: 8.3 },
                        totalTraffic: { value: 15678, change: 15.2 },
                        totalCardConsumption: { value: 567890, change: -2.1 },
                        totalMeituan: { value: 234567, change: 18.7 },
                        totalDouyin: { value: 123456, change: 25.3 },
                        totalOnline: { value: 789012, change: 9.8 },
                        totalOffline: { value: 1567890, change: 6.5 },
                        totalCard: { value: 345678, change: 4.2 },
                        totalReviews: { value: 2345, change: 22.1 }
                    }
                },
                store1: {
                    metrics: {
                        totalRevenue: { value: 456789, change: 10.2 },
                        totalCashFlow: { value: 234567, change: 7.8 },
                        totalTraffic: { value: 2890, change: 12.5 },
                        totalCardConsumption: { value: 89012, change: -1.5 },
                        totalMeituan: { value: 45678, change: 16.3 },
                        totalDouyin: { value: 23456, change: 28.7 },
                        totalOnline: { value: 123456, change: 11.2 },
                        totalOffline: { value: 234567, change: 8.9 },
                        totalCard: { value: 56789, change: 5.1 },
                        totalReviews: { value: 456, change: 19.8 }
                    }
                }
            },
            rankings: {
                revenue: [
                    { name: '北京朝阳店', value: 456789, rank: 1 },
                    { name: '上海浦东店', value: 423456, rank: 2 },
                    { name: '广州天河店', value: 398765, rank: 3 },
                    { name: '深圳南山店', value: 367890, rank: 4 },
                    { name: '杭州西湖店', value: 345678, rank: 5 }
                ],
                traffic: [
                    { name: '上海浦东店', value: 3456, rank: 1 },
                    { name: '北京朝阳店', value: 2890, rank: 2 },
                    { name: '深圳南山店', value: 2678, rank: 3 },
                    { name: '广州天河店', value: 2345, rank: 4 },
                    { name: '杭州西湖店', value: 2123, rank: 5 }
                ],
                staff: [
                    { name: '广州天河店', value: 45, rank: 1 },
                    { name: '北京朝阳店', value: 42, rank: 2 },
                    { name: '上海浦东店', value: 38, rank: 3 },
                    { name: '深圳南山店', value: 35, rank: 4 },
                    { name: '杭州西湖店', value: 32, rank: 5 }
                ],
                commission: [
                    { name: '北京朝阳店', value: 23456, rank: 1 },
                    { name: '深圳南山店', value: 21234, rank: 2 },
                    { name: '上海浦东店', value: 19876, rank: 3 },
                    { name: '广州天河店', value: 18765, rank: 4 },
                    { name: '杭州西湖店', value: 17654, rank: 5 }
                ]
            },
            trends: {
                revenue: {
                    dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
                    values: [180000, 195000, 210000, 185000, 225000, 240000, 285000]
                },
                traffic: {
                    dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
                    values: [1200, 1350, 1480, 1320, 1650, 1780, 1890]
                },
                recharge: {
                    dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
                    values: [45000, 52000, 48000, 61000, 58000, 67000, 72000]
                }
            },
            storeLocations: [
                { name: '北京朝阳店', coord: [116.4074, 39.9042], value: 456789 },
                { name: '上海浦东店', coord: [121.4737, 31.2304], value: 423456 },
                { name: '广州天河店', coord: [113.2644, 23.1291], value: 398765 },
                { name: '深圳南山店', coord: [113.9249, 22.5431], value: 367890 },
                { name: '杭州西湖店', coord: [120.1551, 30.2741], value: 345678 }
            ]
        };

        // 初始化图表
        let mapChart, revenueChart, trafficChart, rechargeChart;

        // 格式化数字
        function formatNumber(num) {
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            }
            return num.toLocaleString();
        }

        // 格式化货币
        function formatCurrency(num) {
            if (num >= 10000) {
                return '¥' + (num / 10000).toFixed(1) + '万';
            }
            return '¥' + num.toLocaleString();
        }

        // 更新日期时间
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                weekday: 'long',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            };
            document.getElementById('datetime').textContent = now.toLocaleDateString('zh-CN', options);
        }

        // 更新指标数据
        function updateMetrics(storeId = 'all') {
            const metrics = mockData.stores[storeId]?.metrics || mockData.stores.all.metrics;

            Object.keys(metrics).forEach(key => {
                const metric = metrics[key];
                const valueElement = document.getElementById(key);
                const changeElement = document.getElementById(key.replace('total', '').toLowerCase() + 'Change');

                if (valueElement) {
                    if (key.includes('Revenue') || key.includes('CashFlow') || key.includes('Consumption') ||
                        key.includes('Meituan') || key.includes('Douyin') || key.includes('Online') ||
                        key.includes('Offline') || key.includes('Card')) {
                        valueElement.textContent = formatCurrency(metric.value);
                    } else if (key.includes('Traffic')) {
                        valueElement.textContent = formatNumber(metric.value) + '人';
                    } else if (key.includes('Reviews')) {
                        valueElement.textContent = formatNumber(metric.value) + '条';
                    }
                }

                if (changeElement) {
                    const isPositive = metric.change > 0;
                    changeElement.className = `metric-change ${isPositive ? 'positive' : 'negative'}`;
                    changeElement.innerHTML = `
                        <span class="change-arrow">${isPositive ? '↗' : '↘'}</span>
                        <span>${isPositive ? '+' : ''}${metric.change}%</span>
                    `;
                }
            });
        }

        // 更新排行榜
        function updateRankings(type = 'revenue') {
            const rankings = mockData.rankings[type];
            const rankingList = document.getElementById('rankingList');

            rankingList.innerHTML = rankings.map(item => {
                const maxValue = Math.max(...rankings.map(r => r.value));
                const percentage = (item.value / maxValue) * 100;

                let rankClass = 'other';
                if (item.rank === 1) rankClass = 'first';
                else if (item.rank === 2) rankClass = 'second';
                else if (item.rank === 3) rankClass = 'third';

                let valueText = '';
                if (type === 'revenue' || type === 'commission') {
                    valueText = formatCurrency(item.value);
                } else if (type === 'traffic') {
                    valueText = formatNumber(item.value) + '人';
                } else if (type === 'staff') {
                    valueText = item.value + '人';
                }

                return `
                    <div class="ranking-item">
                        <div class="ranking-number ${rankClass}">${item.rank}</div>
                        <div class="ranking-info">
                            <div class="ranking-name">${item.name}</div>
                            <div class="ranking-progress">
                                <div class="ranking-progress-bar" style="width: ${percentage}%"></div>
                            </div>
                            <div class="ranking-value">${valueText}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 初始化地图
        function initMapChart() {
            mapChart = echarts.init(document.getElementById('map-chart'));

            const option = {
                backgroundColor: 'transparent',
                geo: {
                    map: 'china',
                    roam: true,
                    zoom: 1.2,
                    center: [104.114129, 37.550339],
                    itemStyle: {
                        areaColor: 'rgba(0, 50, 100, 0.3)',
                        borderColor: 'rgba(0, 150, 255, 0.5)',
                        borderWidth: 1
                    },
                    emphasis: {
                        itemStyle: {
                            areaColor: 'rgba(0, 150, 255, 0.2)'
                        }
                    }
                },
                series: [
                    {
                        type: 'scatter',
                        coordinateSystem: 'geo',
                        data: mockData.storeLocations.map(store => ({
                            name: store.name,
                            value: store.coord.concat(store.value),
                            symbolSize: Math.sqrt(store.value / 10000) + 10
                        })),
                        symbolSize: function(val) {
                            return Math.sqrt(val[2] / 10000) + 15;
                        },
                        itemStyle: {
                            color: '#00d4ff',
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 212, 255, 0.5)'
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#ff6b6b',
                                shadowBlur: 20,
                                shadowColor: 'rgba(255, 107, 107, 0.8)'
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            color: '#ffffff',
                            fontSize: 12,
                            formatter: '{b}'
                        }
                    },
                    {
                        type: 'heatmap',
                        coordinateSystem: 'geo',
                        data: mockData.storeLocations.map(store => ({
                            name: store.name,
                            value: store.coord.concat(store.value / 1000)
                        })),
                        pointSize: 20,
                        blurSize: 30
                    }
                ],
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(0, 20, 40, 0.9)',
                    borderColor: 'rgba(0, 150, 255, 0.5)',
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        if (params.seriesType === 'scatter') {
                            return `${params.name}<br/>营业额: ${formatCurrency(params.value[2])}`;
                        }
                        return params.name;
                    }
                }
            };

            mapChart.setOption(option);
        }

        // 初始化趋势图表
        function initTrendCharts() {
            // 营业额趋势图
            revenueChart = echarts.init(document.getElementById('revenueChart'));
            const revenueOption = {
                backgroundColor: 'transparent',
                grid: {
                    left: '10%',
                    right: '10%',
                    top: '20%',
                    bottom: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: mockData.trends.revenue.dates,
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#a0d8ff'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#a0d8ff',
                        formatter: function(value) {
                            return formatCurrency(value);
                        }
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.1)' }
                    }
                },
                series: [{
                    type: 'line',
                    data: mockData.trends.revenue.values,
                    smooth: true,
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(0, 212, 255, 0.8)' },
                                { offset: 1, color: 'rgba(0, 212, 255, 0.1)' }
                            ]
                        }
                    },
                    lineStyle: {
                        color: '#00d4ff',
                        width: 3
                    },
                    itemStyle: {
                        color: '#00d4ff',
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 20, 40, 0.9)',
                    borderColor: 'rgba(0, 150, 255, 0.5)',
                    textStyle: { color: '#ffffff' },
                    formatter: function(params) {
                        return `${params[0].name}<br/>营业额: ${formatCurrency(params[0].value)}`;
                    }
                }
            };
            revenueChart.setOption(revenueOption);

            // 客流趋势图
            trafficChart = echarts.init(document.getElementById('trafficChart'));
            const trafficOption = {
                backgroundColor: 'transparent',
                grid: {
                    left: '10%',
                    right: '10%',
                    top: '20%',
                    bottom: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: mockData.trends.traffic.dates,
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#a0d8ff'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#a0d8ff',
                        formatter: function(value) {
                            return formatNumber(value) + '人';
                        }
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.1)' }
                    }
                },
                series: [{
                    type: 'line',
                    data: mockData.trends.traffic.values,
                    smooth: true,
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(255, 107, 107, 0.8)' },
                                { offset: 1, color: 'rgba(255, 107, 107, 0.1)' }
                            ]
                        }
                    },
                    lineStyle: {
                        color: '#ff6b6b',
                        width: 3
                    },
                    itemStyle: {
                        color: '#ff6b6b',
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 20, 40, 0.9)',
                    borderColor: 'rgba(0, 150, 255, 0.5)',
                    textStyle: { color: '#ffffff' },
                    formatter: function(params) {
                        return `${params[0].name}<br/>客流: ${formatNumber(params[0].value)}人`;
                    }
                }
            };
            trafficChart.setOption(trafficOption);

            // 充值金额趋势图
            rechargeChart = echarts.init(document.getElementById('rechargeChart'));
            const rechargeOption = {
                backgroundColor: 'transparent',
                grid: {
                    left: '10%',
                    right: '10%',
                    top: '20%',
                    bottom: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: mockData.trends.recharge.dates,
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#a0d8ff'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#a0d8ff',
                        formatter: function(value) {
                            return formatCurrency(value);
                        }
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 150, 255, 0.1)' }
                    }
                },
                series: [{
                    type: 'line',
                    data: mockData.trends.recharge.values,
                    smooth: true,
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(255, 193, 7, 0.8)' },
                                { offset: 1, color: 'rgba(255, 193, 7, 0.1)' }
                            ]
                        }
                    },
                    lineStyle: {
                        color: '#ffc107',
                        width: 3
                    },
                    itemStyle: {
                        color: '#ffc107',
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 20, 40, 0.9)',
                    borderColor: 'rgba(0, 150, 255, 0.5)',
                    textStyle: { color: '#ffffff' },
                    formatter: function(params) {
                        return `${params[0].name}<br/>充值金额: ${formatCurrency(params[0].value)}`;
                    }
                }
            };
            rechargeChart.setOption(rechargeOption);
        }

        // 事件监听器
        function initEventListeners() {
            // 门店选择变化
            document.getElementById('storeSelect').addEventListener('change', function(e) {
                updateMetrics(e.target.value);
            });

            // 时间选择变化
            document.getElementById('timeSelect').addEventListener('change', function(e) {
                // 这里可以根据时间选择更新数据
                updateMetrics();
                updateRankings();
            });

            // 排行榜标签切换
            document.querySelectorAll('.ranking-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.ranking-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    updateRankings(this.dataset.type);
                });
            });

            // 窗口大小变化时重新调整图表
            window.addEventListener('resize', function() {
                if (mapChart) mapChart.resize();
                if (revenueChart) revenueChart.resize();
                if (trafficChart) trafficChart.resize();
                if (rechargeChart) rechargeChart.resize();
            });
        }

        // 页面初始化
        function init() {
            // 更新日期时间
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // 等待ECharts加载完成后初始化图表
            if (typeof echarts !== 'undefined') {
                // 注册中国地图
                fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json')
                    .then(response => response.json())
                    .then(geoJson => {
                        echarts.registerMap('china', geoJson);
                        initMapChart();
                    })
                    .catch(error => {
                        console.warn('地图数据加载失败，使用默认配置', error);
                        initMapChart();
                    });

                initTrendCharts();
            }

            // 初始化数据
            updateMetrics();
            updateRankings();

            // 初始化事件监听器
            initEventListeners();

            // 添加一些动态效果
            setTimeout(() => {
                document.querySelectorAll('.metric-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';
                        card.style.transition = 'all 0.5s ease';
                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 50);
                    }, index * 100);
                });
            }, 500);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>